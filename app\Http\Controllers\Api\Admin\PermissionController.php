<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PermissionGroup;
use App\Repositories\Permission\PermissionRepositoryInterface;
use App\Enums\PermissionGroupStatus;
use App\Enums\PermissionIsHidden;
use Illuminate\Support\Facades\Auth;

class PermissionController extends Controller
{
    private $permissionRepository;

    public function __construct(
        PermissionRepositoryInterface $permissionRepository,
    )
    {
        $this->permissionRepository = $permissionRepository;
    }
    
    public function getAllPermissions(Request $request)
    {
        $dataSearch = $request->all();
        $result = $this->permissionRepository->getAllPermissions($dataSearch);
        if (isset($result)) {
            
            return response()->json([
                'status' => 'success',
                'counts' => $result['counts'],
                'permissions' => $result['permissions'],
            ], 200);
        } else {

            return response()->json(['status' => 'not_data'], 200);
        }
    }

    public function storePermission(Request $request)
    {
        try {
            $data_permission = [
                'name' => $request->name,
                'slug' => $request->slug,
                'permission_group_id' => $request->permission_group_id,
                'is_hidden' => $request->is_hidden,
                'is_active' => $request->is_active,
                'create_by' => Auth::id(),
            ];

            $permission = $this->permissionRepository->create($data_permission);
            
            return response()->json([
                'status' => 'success',
                'data' => $permission,
            ], 201);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'data' => $th,
            ], 500);
        }
    }

    /**
     * Get all permission groups
     */
    public function getPermissionGroups(Request $request)
    {
        try {
            $query = PermissionGroup::select('id', 'name')
                // ->whereHas('permissions', function($q) {
                //     $q->where('is_hidden', PermissionIsHidden::SHOW->value);
                // })
                ->with(['permissions' => function($q) {
                    $q->select('id', 'name', 'permission_group_id')->where('is_hidden', PermissionIsHidden::SHOW->value);
                }]);
            // Get all permission groups without pagination
            $permissionGroups = $query->get();

            return response()->json([
                'status' => 'success',
                'permission_groups' => $permissionGroups,
            ], 200);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get permission groups',
                'data' => $th->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a new permission group
     */
    public function storePermissionGroup(Request $request)
    {
        try {
            $permissionGroup = PermissionGroup::create([
                'name' => $request->name,
                'is_active' => PermissionGroupStatus::TRUE->value,
                'create_by' => Auth::id(),
            ]);

            return response()->json([
                'status' => 'success',
                'data' => $permissionGroup,
            ], 201);

        } catch (\Throwable $th) {
            return response()->json([
                'status' => 'error',
                'data' => $th->getMessage(),
            ], 500);
        }
    }
}
